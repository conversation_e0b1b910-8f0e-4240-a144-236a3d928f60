#!/usr/bin/env python3
"""
Test different GEMWiki search query patterns to find the best approach
"""

import os
import sys
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Load environment
from dotenv import load_dotenv
load_dotenv()

def test_gemwiki_queries():
    """Test different GEMWiki search query patterns"""
    print("🔍 Testing GEMWiki Search Query Patterns")
    print("=" * 60)
    
    try:
        from agent.registry_nodes import get_web_search_function
        
        # Get the web search function
        web_search_fn = get_web_search_function()
        
        plant_name = "Baldwin Energy Complex"
        
        # Different query patterns to test
        query_patterns = [
            f"{plant_name} site:gem.wiki",
            f"{plant_name} site:globalenergymonitor.org",
            f'"{plant_name}" gem wiki',
            f'"{plant_name}" global energy monitor',
            f"{plant_name} power plant gem.wiki units",
            f"{plant_name} coal plant globalenergymonitor",
            f"Baldwin Energy gem.wiki specifications",
            f"Baldwin Energy Complex Illinois gem.wiki",
            f"Baldwin Energy Complex Dynegy gem.wiki",
            f"Baldwin power plant global energy monitor"
        ]
        
        successful_queries = []
        
        for i, query in enumerate(query_patterns, 1):
            print(f"\n🔍 Query {i}/{len(query_patterns)}: {query}")
            
            try:
                results = web_search_fn(query)
                
                if results:
                    for result in results:
                        content = result.get('content', '')
                        
                        # Check for GEMWiki indicators
                        gem_wiki_indicators = [
                            'gem.wiki', 'global energy monitor', 'gem wiki', 
                            'globalenergymonitor', 'globalenergymonitor.org'
                        ]
                        
                        has_gem_wiki = any(indicator in content.lower() for indicator in gem_wiki_indicators)
                        
                        # Check for power plant specific content
                        power_plant_indicators = [
                            'capacity', 'MW', 'megawatt', 'unit', 'coal', 
                            'power plant', 'operational', 'commissioned'
                        ]
                        
                        has_plant_data = any(indicator in content.lower() for indicator in power_plant_indicators)
                        
                        print(f"   📄 Content length: {len(content)} chars")
                        print(f"   🎯 Has GEMWiki data: {'✅' if has_gem_wiki else '❌'}")
                        print(f"   🏭 Has plant data: {'✅' if has_plant_data else '❌'}")
                        
                        if has_gem_wiki and has_plant_data:
                            successful_queries.append(query)
                            print(f"   🎉 SUCCESS! This query found relevant GEMWiki data")
                            
                            # Show a sample of the content
                            print(f"   📋 Sample content:")
                            sample_content = content[:500]
                            print(f"      {sample_content}...")
                        
                        # Check for specific Baldwin Energy Complex mentions
                        if 'baldwin' in content.lower() and 'energy' in content.lower():
                            print(f"   🎯 Found Baldwin Energy Complex mention!")
                            
                else:
                    print(f"   ❌ No results returned")
                    
            except Exception as e:
                print(f"   ❌ Query failed: {e}")
        
        print(f"\n📊 Summary")
        print("=" * 50)
        print(f"Total queries tested: {len(query_patterns)}")
        print(f"Successful queries: {len(successful_queries)}")
        
        if successful_queries:
            print(f"\n✅ Best working queries:")
            for i, query in enumerate(successful_queries, 1):
                print(f"   {i}. {query}")
        else:
            print(f"\n❌ No queries successfully found GEMWiki data for Baldwin Energy Complex")
            print(f"\nPossible reasons:")
            print(f"   - Baldwin Energy Complex may not be in GEMWiki database")
            print(f"   - Plant may be listed under a different name")
            print(f"   - GEMWiki may not have detailed unit information for this plant")
            
            print(f"\n💡 Suggestions:")
            print(f"   - Try searching for the parent company 'Dynegy'")
            print(f"   - Try alternative plant names or locations")
            print(f"   - Check if the plant is listed in other energy databases")
        
        return len(successful_queries) > 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_alternative_plant_names():
    """Test searches with alternative plant names and company names"""
    print("\n🔍 Testing Alternative Plant Names")
    print("=" * 60)
    
    try:
        from agent.registry_nodes import get_web_search_function
        web_search_fn = get_web_search_function()
        
        # Alternative names to try
        alternative_names = [
            "Dynegy Baldwin Energy Complex",
            "Baldwin Power Plant Illinois",
            "Baldwin Station Illinois",
            "Dynegy Baldwin Station",
            "Baldwin Energy Facility",
            "Baldwin Coal Plant Illinois",
            "Dynegy Midwest Generation Baldwin"
        ]
        
        successful_alternatives = []
        
        for name in alternative_names:
            print(f"\n🔍 Testing: {name}")
            
            queries = [
                f"{name} site:gem.wiki",
                f"{name} site:globalenergymonitor.org",
                f'"{name}" gem wiki'
            ]
            
            for query in queries:
                try:
                    results = web_search_fn(query)
                    
                    if results:
                        for result in results:
                            content = result.get('content', '')
                            
                            # Check for relevant content
                            has_gem_wiki = any(indicator in content.lower() for indicator in [
                                'gem.wiki', 'global energy monitor', 'globalenergymonitor'
                            ])
                            
                            has_baldwin = 'baldwin' in content.lower()
                            has_plant_data = any(indicator in content.lower() for indicator in [
                                'capacity', 'MW', 'unit', 'coal', 'power plant'
                            ])
                            
                            if has_gem_wiki and (has_baldwin or has_plant_data):
                                print(f"   ✅ Found data with query: {query}")
                                successful_alternatives.append((name, query))
                                
                                # Show sample
                                sample = content[:300]
                                print(f"   📋 Sample: {sample}...")
                                break
                                
                except Exception as e:
                    print(f"   ❌ Query failed: {e}")
        
        print(f"\n📊 Alternative Names Summary")
        print("=" * 50)
        print(f"Successful alternatives: {len(successful_alternatives)}")
        
        if successful_alternatives:
            print(f"\n✅ Working alternatives:")
            for name, query in successful_alternatives:
                print(f"   - {name}: {query}")
        
        return len(successful_alternatives) > 0
        
    except Exception as e:
        print(f"❌ Alternative names test failed: {e}")
        return False

def main():
    print("🧪 GEMWiki Query Pattern Testing")
    print("=" * 60)
    
    # Test 1: Different query patterns
    patterns_work = test_gemwiki_queries()
    
    # Test 2: Alternative plant names
    alternatives_work = test_alternative_plant_names()
    
    # Final recommendation
    print(f"\n🎯 Final Recommendation")
    print("=" * 60)
    
    if patterns_work or alternatives_work:
        print("✅ Found working GEMWiki search patterns!")
        print("   The system should be able to retrieve GEMWiki data with the right queries.")
    else:
        print("❌ No working GEMWiki queries found for Baldwin Energy Complex")
        print("\n💡 This explains why you're seeing 'No GEMWiki data - assuming all units operational'")
        print("\nPossible solutions:")
        print("1. Baldwin Energy Complex may not be in GEMWiki database")
        print("2. Try using a different test plant that's known to be in GEMWiki")
        print("3. Enhance the search query generation to try more variations")
        print("4. Add fallback to other reliable power plant databases")

if __name__ == "__main__":
    main()
