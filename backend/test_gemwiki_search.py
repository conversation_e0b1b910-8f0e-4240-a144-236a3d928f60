#!/usr/bin/env python3
"""
Test GEMWiki search functionality to diagnose why it's not working
"""

import os
import sys
from pathlib import Path

# Add the src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Load environment
from dotenv import load_dotenv
load_dotenv()

def test_environment_variables():
    """Test if required environment variables are set"""
    print("🔍 Testing Environment Variables")
    print("=" * 50)
    
    required_vars = [
        'GEMINI_API_KEY',
        'SCRAPER_API_KEY'
    ]
    
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * 10}...{value[-4:] if len(value) > 4 else '****'}")
        else:
            print(f"❌ {var}: Not set")
    
    return all(os.getenv(var) for var in required_vars)

def test_gemini_search():
    """Test Gemini Google Search API"""
    print("\n🔍 Testing Gemini Google Search API")
    print("=" * 50)
    
    try:
        from google.ai.generativelanguage_v1beta.types import client as genai_client
        from google.ai.generativelanguage_v1beta import Client
        
        # Initialize client
        client = Client(api_key=os.getenv("GEMINI_API_KEY"))
        
        # Test search query for GEMWiki
        test_query = "Baldwin Energy Complex site:gem.wiki"
        print(f"🔍 Testing query: {test_query}")
        
        search_prompt = f"""Search the web for: {test_query}

Provide comprehensive search results with specific focus on power plant unit specifications and operational data."""

        response = client.models.generate_content(
            model="gemini-2.0-flash-exp",
            contents=search_prompt,
            config={
                "tools": [{"google_search": {}}],
                "temperature": 0.1,
            }
        )
        
        if response and response.text:
            print(f"✅ Search successful!")
            print(f"📄 Response length: {len(response.text)} characters")
            print(f"📋 First 500 characters:")
            print(response.text[:500])
            print("...")
            
            # Check for GEMWiki indicators
            gem_wiki_indicators = ['gem.wiki', 'global energy monitor', 'gem wiki', 'globalenergymonitor']
            has_gem_wiki = any(indicator in response.text.lower() for indicator in gem_wiki_indicators)
            
            print(f"\n🎯 GEMWiki Detection:")
            print(f"   Has GEMWiki data: {'✅' if has_gem_wiki else '❌'}")
            
            if has_gem_wiki:
                for indicator in gem_wiki_indicators:
                    if indicator in response.text.lower():
                        print(f"   Found indicator: '{indicator}'")
            
            return True
        else:
            print("❌ No response received")
            return False
            
    except Exception as e:
        print(f"❌ Gemini search failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_scraper_api():
    """Test ScraperAPI functionality"""
    print("\n🔍 Testing ScraperAPI")
    print("=" * 50)
    
    try:
        import requests
        import certifi
        
        scraper_api_key = os.getenv('SCRAPER_API_KEY')
        if not scraper_api_key:
            print("❌ SCRAPER_API_KEY not set")
            return False
        
        # Test query
        test_query = "Baldwin Energy Complex gem.wiki"
        payload = {
            'api_key': scraper_api_key, 
            'query': test_query, 
            'page': 1, 
            'num': 5
        }
        
        print(f"🔍 Testing query: {test_query}")
        
        response = requests.get(
            'https://api.scraperapi.com/structured/google/search', 
            params=payload, 
            verify=certifi.where(),
            timeout=30
        )
        
        print(f"📊 Status code: {response.status_code}")
        
        if response.status_code == 200:
            results = response.json()
            organic_results = results.get('organic_results', [])
            
            print(f"✅ ScraperAPI successful!")
            print(f"📄 Found {len(organic_results)} organic results")
            
            gem_wiki_found = False
            for i, result in enumerate(organic_results[:3]):
                title = result.get('title', 'No title')
                link = result.get('link', 'No link')
                print(f"   {i+1}. {title}")
                print(f"      URL: {link}")
                
                if 'gem.wiki' in link.lower() or 'globalenergymonitor' in link.lower():
                    gem_wiki_found = True
                    print(f"      🎯 GEMWiki result found!")
            
            print(f"\n🎯 GEMWiki Detection:")
            print(f"   Found GEMWiki results: {'✅' if gem_wiki_found else '❌'}")
            
            return True
        else:
            print(f"❌ ScraperAPI failed with status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ ScraperAPI test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_web_search_function():
    """Test the actual web search function used by the system"""
    print("\n🔍 Testing System Web Search Function")
    print("=" * 50)
    
    try:
        from agent.registry_nodes import get_web_search_function
        
        # Get the web search function
        web_search_fn = get_web_search_function()
        
        # Test search
        test_query = "Baldwin Energy Complex site:gem.wiki units specifications"
        print(f"🔍 Testing query: {test_query}")
        
        results = web_search_fn(test_query)
        
        if results:
            print(f"✅ Web search successful!")
            print(f"📄 Found {len(results)} results")
            
            for i, result in enumerate(results):
                title = result.get('title', 'No title')
                content = result.get('content', 'No content')
                print(f"   {i+1}. {title}")
                print(f"      Content length: {len(content)} characters")
                print(f"      First 200 chars: {content[:200]}...")
                
                # Check for GEMWiki indicators
                gem_wiki_indicators = ['gem.wiki', 'global energy monitor', 'gem wiki', 'globalenergymonitor']
                has_gem_wiki = any(indicator in content.lower() for indicator in gem_wiki_indicators)
                print(f"      Has GEMWiki data: {'✅' if has_gem_wiki else '❌'}")
            
            return True
        else:
            print("❌ No results returned")
            return False
            
    except Exception as e:
        print(f"❌ Web search function test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🧪 GEMWiki Search Diagnostic Test")
    print("=" * 60)
    
    # Test 1: Environment variables
    env_ok = test_environment_variables()
    
    if not env_ok:
        print("\n❌ Environment variables missing. Please check your .env file.")
        return
    
    # Test 2: Gemini search
    gemini_ok = test_gemini_search()
    
    # Test 3: ScraperAPI
    scraper_ok = test_scraper_api()
    
    # Test 4: System web search function
    system_ok = test_web_search_function()
    
    # Summary
    print("\n📊 Test Summary")
    print("=" * 50)
    print(f"Environment Variables: {'✅' if env_ok else '❌'}")
    print(f"Gemini Search API: {'✅' if gemini_ok else '❌'}")
    print(f"ScraperAPI: {'✅' if scraper_ok else '❌'}")
    print(f"System Web Search: {'✅' if system_ok else '❌'}")
    
    if all([env_ok, gemini_ok or scraper_ok, system_ok]):
        print("\n🎉 All tests passed! GEMWiki search should be working.")
    else:
        print("\n⚠️ Some tests failed. This explains why GEMWiki data is not being retrieved.")
        print("\nTroubleshooting suggestions:")
        if not env_ok:
            print("- Check your .env file for GEMINI_API_KEY and SCRAPER_API_KEY")
        if not gemini_ok:
            print("- Verify GEMINI_API_KEY is valid and has search permissions")
        if not scraper_ok:
            print("- Verify SCRAPER_API_KEY is valid and has remaining credits")
        if not system_ok:
            print("- Check the web search function implementation")

if __name__ == "__main__":
    main()
